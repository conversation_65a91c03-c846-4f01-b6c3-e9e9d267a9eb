import {
    getListMessageService,
    sendMessageService,
    readCommentService,
    searchMessagesService,
    removeCommentService,
    attachFileService,
    getBranchService
} from '@services'
import {
    IMessageRequest,
    IMessageResponse,
    ISendMessageResponse,
    ISendMessageRequest,
    IReadCommentRequest,
    IReadCommentResponse,
    ISearchMessageRequest,
    ISearchMessageResponse,
    IRemoveCommentRequest,
    IRemoveCommentResponse,
    IAttachFilePayload,
    IAttachFileResponse,
    IMessage,
    IStoreBranch
} from '@types'

export const GET_LIST_MESSAGE_SUCCESS = 'GET_LIST_MESSAGE_SUCCESS'
export const GET_LIST_MESSAGE_FAIL = 'GET_LIST_MESSAGE_FAIL'
export const GET_LIST_MESSAGE_PENDING = 'GET_LIST_MESSAGE_PENDING'

export const RESET_LIST_MESSAGE = 'RESET_LIST_MESSAGE'

export const UPDATE_LIST_MESSAGE = 'UPDATE_LIST_MESSAGE'

export const SEND_MESSAGE_SUCCESS = 'SEND_MESSAGE_SUCCESS'
export const SEND_MESSAGE_FAIL = 'SEND_MESSAGE_FAIL'
export const SEND_MESSAGE_PENDING = 'SEND_MESSAGE_PENDING'

export const READ_COMMENT_SUCCESS = 'READ_COMMENT_SUCCESS'
export const READ_COMMENT_FAIL = 'READ_COMMENT_FAIL'
export const READ_COMMENT_PENDING = 'READ_COMMENT_PENDING'

export const SEARCH_MESSAGES_SUCCESS = 'SEARCH_MESSAGES_SUCCESS'
export const SEARCH_MESSAGES_FAIL = 'SEARCH_MESSAGES_FAIL'
export const SEARCH_MESSAGES_PENDING = 'SEARCH_MESSAGES_PENDING'
export const NAVIGATE_SEARCH_RESULT = 'NAVIGATE_SEARCH_RESULT'
export const SEARCH_MESSAGE_BY_ID_SUCCESS = 'SEARCH_MESSAGE_BY_ID_SUCCESS'
export const SEARCH_MESSAGE_BY_ID_FAIL = 'SEARCH_MESSAGE_BY_ID_FAIL'
export const SEARCH_MESSAGE_BY_ID_PENDING = 'SEARCH_MESSAGE_BY_ID_PENDING'
export const CLEAR_SEARCH_HIGHLIGHT = 'CLEAR_SEARCH_HIGHLIGHT'

export const REMOVE_COMMENT_SUCCESS = 'REMOVE_COMMENT_SUCCESS'
export const REMOVE_COMMENT_FAIL = 'REMOVE_COMMENT_FAIL'
export const REMOVE_COMMENT_PENDING = 'REMOVE_COMMENT_PENDING'

export const ATTACH_FILE_SUCCESS = 'ATTACH_FILE_SUCCESS'
export const ATTACH_FILE_FAIL = 'ATTACH_FILE_FAIL'
export const ATTACH_FILE_PENDING = 'ATTACH_FILE_PENDING'

export const GET_STORE_BRANCH_SUCCESS = 'GET_STORE_BRANCH_SUCCESS'
export const GET_STORE_BRANCH_FAIL = 'GET_STORE_BRANCH_FAIL'
export const GET_STORE_BRANCH_PENDING = 'GET_STORE_BRANCH_PENDING'

export const getListMessage = (request: IMessageRequest, isLoading = true) => {
    return async (dispatch: (action: any) => void, getState: () => any) => {
        try {
            isLoading &&
                dispatch({ type: GET_LIST_MESSAGE_PENDING, payload: request })
            const response: IMessageResponse[] = await getListMessageService(
                request
            )
            dispatch({
                type: GET_LIST_MESSAGE_SUCCESS,
                payload: {
                    ...response,
                    pageRequest: request.pageRequest
                }
            })
            return response
        } catch (error) {
            dispatch({ type: GET_LIST_MESSAGE_FAIL, payload: error })
            throw error
        }
    }
}

export const resetListMessage = () => {
    return (dispatch: (action: any) => void) => {
        dispatch({ type: RESET_LIST_MESSAGE })
    }
}

export const updateListMessage = (
    newMessage: IMessage,
    type?: 'message' | 'delete' | 'read'
) => {
    return (dispatch: (action: any) => void, getState: () => any) => {
        dispatch({
            type: UPDATE_LIST_MESSAGE,
            payload: {
                newMessage,
                type
            }
        })
    }
}

export const sendMessage = (request: ISendMessageRequest) => {
    return async (dispatch: (action: any) => void) => {
        try {
            dispatch({ type: SEND_MESSAGE_PENDING })
            const response: ISendMessageResponse = await sendMessageService(
                request
            )
            dispatch({ type: SEND_MESSAGE_SUCCESS, payload: response })
            return response
        } catch (error) {
            dispatch({ type: SEND_MESSAGE_FAIL, payload: error })
            throw error
        }
    }
}

export const readComment = (request: IReadCommentRequest) => {
    return async (dispatch: (action: any) => void) => {
        try {
            dispatch({ type: READ_COMMENT_PENDING })
            const response: IReadCommentResponse = await readCommentService(
                request
            )
            dispatch({ type: READ_COMMENT_SUCCESS, payload: response })
            return response
        } catch (error) {
            dispatch({ type: READ_COMMENT_FAIL, payload: error })
            throw error
        }
    }
}

export const searchMessages = (request: ISearchMessageRequest) => {
    return async (dispatch: (action: any) => void) => {
        try {
            dispatch({ type: SEARCH_MESSAGES_PENDING })
            const response: ISearchMessageResponse =
                await searchMessagesService(request)
            dispatch({ type: SEARCH_MESSAGES_SUCCESS, payload: response })
            return response
        } catch (error) {
            dispatch({ type: SEARCH_MESSAGES_FAIL, payload: error })
            throw error
        }
    }
}

export const navigateSearchResult = (direction: 'next' | 'prev') => {
    return (dispatch: (action: any) => void, getState: () => any) => {
        const state = getState()
        const { searchMessages } = state.supportTicketReducer
        const { foundPositions, currentSearchIndex } = searchMessages

        if (foundPositions.length === 0) return

        let newIndex = currentSearchIndex

        if (
            direction === 'next' &&
            currentSearchIndex < foundPositions.length - 1
        ) {
            newIndex = currentSearchIndex + 1
        } else if (direction === 'prev' && currentSearchIndex > 0) {
            newIndex = currentSearchIndex - 1
        }

        if (newIndex !== currentSearchIndex) {
            dispatch({
                type: NAVIGATE_SEARCH_RESULT,
                payload: {
                    currentSearchIndex: newIndex
                }
            })
        }
    }
}

export const searchMessageById = (commentID: number) => {
    return async (dispatch: (action: any) => void, getState: () => any) => {
        const { typeCode } = getState().commonReducer
        try {
            dispatch({ type: SEARCH_MESSAGE_BY_ID_PENDING })
            const state = getState()
            const { getListMessage } = state.supportTicketReducer
            const supportChatTicketID =
                getListMessage?.data[0]?.supportChatTicketID || -1

            const request: ISearchMessageRequest = {
                supportServiceCode: typeCode,
                supportChatTicketID: supportChatTicketID,
                messageID: commentID,
                supportServiceID: -1,
                actionEvent: null,
                fromTime: -1,
                toTime: -1,
                pageRequest: {
                    iDisplayStart: 0,
                    iDisplayLength: 15,
                    search: ''
                },
                pageSizeMsg: 15
            }

            const response: ISearchMessageResponse =
                await searchMessagesService(request)
            dispatch({ type: SEARCH_MESSAGE_BY_ID_SUCCESS, payload: response })
            return response
        } catch (error) {
            dispatch({ type: SEARCH_MESSAGE_BY_ID_FAIL, payload: error })
            throw error
        }
    }
}

export const removeComment = (commentId: number) => {
    return async (dispatch: (action: any) => void) => {
        try {
            // dispatch({ type: REMOVE_COMMENT_PENDING })
            const request: IRemoveCommentRequest = {
                commentId: commentId
            }
            const response: IRemoveCommentResponse = await removeCommentService(
                request
            )
            dispatch({
                type: REMOVE_COMMENT_SUCCESS,
                payload: { commentId, response }
            })
            return response
        } catch (error) {
            dispatch({ type: REMOVE_COMMENT_FAIL, payload: error })
            throw error
        }
    }
}

export const clearSearchHighlight = () => {
    return (dispatch: (action: any) => void) => {
        dispatch({ type: CLEAR_SEARCH_HIGHLIGHT })
    }
}

export const attachFile = (file: any, posterFile: any = null) => {
    return async (dispatch: (action: any) => void, getState: () => any) => {
        const { typeCode } = getState().commonReducer
        try {
            dispatch({ type: ATTACH_FILE_PENDING })

            const state = getState()
            const { getListMessage } = state.supportTicketReducer
            const supportChatTicketID =
                getListMessage?.data[0]?.supportChatTicketID || -1

            const payload: IAttachFilePayload = {
                supportServiceCode: typeCode,
                supportChatTicketID: supportChatTicketID
            }

            const response: IAttachFileResponse = await attachFileService(
                file,
                posterFile,
                payload
            )

            dispatch({ type: ATTACH_FILE_SUCCESS, payload: response })

            return response
        } catch (error) {
            dispatch({ type: ATTACH_FILE_FAIL, payload: error })
            throw error
        }
    }
}

export const getStoreBranch = () => {
    return async (dispatch: (action: any) => void) => {
        try {
            dispatch({ type: GET_STORE_BRANCH_PENDING })
            const response: IStoreBranch = await getBranchService()
            dispatch({ type: GET_STORE_BRANCH_SUCCESS, payload: response })
            return response
        } catch (error) {
            dispatch({ type: GET_STORE_BRANCH_FAIL, payload: error })
            throw error
        }
    }
}
