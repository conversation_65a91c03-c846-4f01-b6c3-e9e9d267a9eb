export interface ICommonState {
    isDoneSetProps: boolean
    navigation?: any
    Toast: any
    typeCode: string
    name: string
    profile: any
    VideoPlayer: any
}

export type CommonActionTypes = { type: string; payload: any }

export interface IError {
    code: number
    error: string
    errorReason: string
    errorServiceReason: string
}

export interface IStandardReducer {
    loading: boolean
    error: IError
    data: any
}

export const initError: IError = {
    code: 0,
    error: '',
    errorReason: '',
    errorServiceReason: ''
}

export interface IPageRequest {
    iDisplayStart: number
    iDisplayLength: number
    search: string
}

export interface IMessageRequest {
    supportServiceCode: string
    pageRequest: IPageRequest
}

export interface IMessageResponse {
    draw: number
    recordsTotal: number
    recordsFiltered: number
    data: IMessage[]
}

export interface IMessage {
    id: number
    supportChatTicketID: number
    content: string
    contentType: string
    createTime: string
    creatorID: number
    creatorUsername: string
    creatorName: string
    creatorAvatar: string
    isSeen: boolean
    status: string
    seenUserArray: ISeenUser[]
    fromComment?: IFromComment
}

export interface ISeenUser {
    userId: number
    seenTime: string
    userName: string
    userImage: string
    seenTimeLong: number
    userLastName: string
    userFirstName: string
}

export interface IFromComment {
    commentID: number
    creatorID: number
    creatorUserName: string
    creatorName: string
    creatorAvatar: string
    content: string
    contentType: string
    pageIndex: number
    positionMsg: number
}

export interface ISendMessageRequest {
    supportServiceCode: string
    message: string
    supportChatTicketID: number
    fromMessage?: IFromComment
}

export interface ISendMessageResponse {
    id: number
    supportChatTicketID: number
    content: string
    contentType: string
    createTime: string
    creatorID: number
    creatorUsername: string
    creatorName: string
    creatorAvatar: string
    isSeen: boolean
    status: string
    seenUserArray: ISeenUser[]
    fromComment?: IFromComment
}

export interface IReadCommentRequest {
    supportChatTicketID: number
}

export interface IReadCommentResponse {
    success: boolean
}

export interface IRemoveCommentRequest {
    commentId: number
}

export interface IRemoveCommentResponse {
    success: boolean
}

export interface IMessagePosition {
    position: number
    messageID: number
    pageIndex: number
}

export interface ISearchMessageRequest {
    supportServiceCode: string
    supportChatTicketID: number
    supportServiceID?: number
    actionEvent?: any
    fromTime?: number
    toTime?: number
    messageID?: number
    pageRequest: IPageRequest
    pageSizeMsg: number
}

export interface ISearchMessageResponse {
    draw: number
    recordsTotal: number
    recordsFiltered: number
    data: ISearchMessageResult[]
    targetTotal: number
    candidateTotal: number
}

export interface ISearchMessageResult {
    id: number
    creatorID: number
    creatorUsername: string
    creatorName: string
    creatorAvatar: string
    supportServiceID: number
    subject: string
    createTime: string
    arrayPositionMsgFound: IMessagePosition[]
    totalMsgFound: number
    maxCreateMsgTime: string
}

export interface IAttachFilePayload {
    supportServiceCode: string
    supportChatTicketID: number
}

export interface IAttachFileResponse {
    id: number
    supportChatTicketID: number
    content: string
    contentType: string
    createTime: string
    creatorID: number
    creatorUsername: string
    creatorName: string
    creatorAvatar: string
    isSeen: boolean
    status: string
    seenUserArray: ISeenUser[]
    fromComment?: IFromComment
}

export interface IStoreBranch {
    id: number
    code: string
    name: string
    isNotifyAssignAll: boolean
    isNotifyInviteAll: boolean
    backgroundImage: string
    priority: number
    isMuteNotify: boolean
}
