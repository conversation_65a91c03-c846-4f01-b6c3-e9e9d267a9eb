import { apiBase, METHOD } from '@sdk/api'

import {
    IMessageRequest,
    IMessageResponse,
    ISendMessageRequest,
    ISendMessageResponse,
    IReadCommentRequest,
    IReadCommentResponse,
    ISearchMessageRequest,
    ISearchMessageResponse,
    IRemoveCommentRequest,
    IRemoveCommentResponse,
    IAttachFilePayload,
    IAttachFileResponse,
    IStoreBranch
} from '@types'
import {
    GET_LIST_MESSAGE,
    SEND_MESSAGE,
    READ_COMMENT,
    SEARCH_MESSAGES,
    REMOVE_COMMENT,
    ATTACH_FILE,
    GET_STORE_BRANCH
} from '../api'
import AsyncStorage from '@react-native-async-storage/async-storage'

const setCustomError = (error: any) => {
    if (error?.code === -1) {
        error.errorReason = 'Đã có lỗi xảy ra, vui lòng thử lại sau'
    }
    return error
}

export const getListMessageService = async (request: IMessageRequest) => {
    return new Promise<IMessageResponse[]>(async (resolve, reject) => {
        try {
            const response: any = await apiBase(
                GET_LIST_MESSAGE,
                METHOD.POST,
                request
            )
            if (response?.object) resolve(response.object)
            else throw {}
        } catch (error: any) {
            const ERROR = setCustomError(error)
            console.log('getListMessageService error', ERROR)
            reject(ERROR)
        }
    })
}

export const sendMessageService = async (request: ISendMessageRequest) => {
    return new Promise<ISendMessageResponse>(async (resolve, reject) => {
        try {
            const response: any = await apiBase(
                SEND_MESSAGE,
                METHOD.POST,
                request
            )
            if (response?.object) resolve(response.object)
            else throw {}
        } catch (error: any) {
            const ERROR = setCustomError(error)
            console.log('sendMessageService error', ERROR)
            reject(ERROR)
        }
    })
}

export const readCommentService = async (request: IReadCommentRequest) => {
    return new Promise<IReadCommentResponse>(async (resolve, reject) => {
        try {
            const response: any = await apiBase(
                READ_COMMENT,
                METHOD.POST,
                request
            )
            if (response?.object) resolve(response.object)
            else throw {}
        } catch (error: any) {
            const ERROR = setCustomError(error)
            console.log('readCommentService error', ERROR)
            reject(ERROR)
        }
    })
}

export const searchMessagesService = async (request: ISearchMessageRequest) => {
    return new Promise<ISearchMessageResponse>(async (resolve, reject) => {
        try {
            const response: any = await apiBase(
                SEARCH_MESSAGES,
                METHOD.POST,
                request
            )
            if (response?.object) resolve(response.object)
            else throw {}
        } catch (error: any) {
            const ERROR = setCustomError(error)
            console.log('searchMessagesService error', ERROR)
            reject(ERROR)
        }
    })
}

export const removeCommentService = async (request: IRemoveCommentRequest) => {
    return new Promise<IRemoveCommentResponse>(async (resolve, reject) => {
        try {
            const response: any = await apiBase(
                REMOVE_COMMENT,
                METHOD.POST,
                request
            )
            if (response?.object || !response?.error) resolve(response.object)
            else throw {}
        } catch (error: any) {
            const ERROR = setCustomError(error)
            console.log('removeCommentService error', ERROR)
            reject(ERROR)
        }
    })
}

export const attachFileService = async (
    file: any,
    posterFile: any,
    payload: IAttachFilePayload
) => {
    return new Promise<IAttachFileResponse>(async (resolve, reject) => {
        try {
            // Create FormData
            const formData = new FormData()

            const token = await AsyncStorage.getItem('TOKEN_ACCESS')

            // Add file
            if (file) {
                formData.append('file', {
                    uri: file.uri,
                    type: file.type || 'application/octet-stream',
                    name: file.name || 'file'
                })
            }

            // Add poster file if available
            if (posterFile) {
                formData.append('posterfile', {
                    uri: posterFile.uri,
                    type: posterFile.type || 'image/jpeg',
                    name: posterFile.name || 'poster.jpg'
                })
            }

            // Add payload as JSON string
            formData.append('payload', JSON.stringify(payload))

            // Make API call with FormData
            const response: any = await fetch(ATTACH_FILE, {
                method: 'POST',
                headers: {
                    'Content-Type': 'multipart/form-data',
                    Authorization: `Bearer ${token || ''}`
                },
                body: formData
            }).then((res) => res.json())

            if (response?.object) resolve(response.object)
            else throw response
        } catch (error: any) {
            const ERROR = setCustomError(error)
            console.log('attachFileService error', ERROR)
            reject(ERROR)
        }
    })
}

export const getBranchService = async () => {
    return new Promise<IStoreBranch>(async (resolve, reject) => {
        try {
            const response: any = await apiBase(
                GET_STORE_BRANCH,
                METHOD.POST,
                {}
            )
            if (response?.object) resolve(response.object)
            else throw {}
        } catch (error: any) {
            const ERROR = setCustomError(error)
            console.log('getBranchService error', ERROR)
            reject(ERROR)
        }
    })
}
