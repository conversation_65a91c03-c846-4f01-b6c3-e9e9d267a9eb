import React, { memo, useEffect, useState } from 'react'
import {
    View,
    TouchableOpacity,
    StyleSheet,
    NativeSyntheticEvent,
    TextInputKeyPressEventData
} from 'react-native'
import { Colors, Mixins, SearchInput, MyText } from '@react-native-xwork'
import { Image } from '@mwg-kits/components'
import IMAGES from '../../assets/images'
import { IFromComment } from '@types'
import { BASE_AVATAR_URL } from '../../api'
import AttachmentOptions from './AttachmentOptions'

// Hàm cắt ngắn tên file nếu quá dài
const getTruncatedFileName = (fileName: string) => {
    if (!fileName) return ''
    if (fileName.length <= 20) return fileName

    // Lấy phần mở rộng của file
    const lastDotIndex = fileName.lastIndexOf('.')
    const extension =
        lastDotIndex !== -1 ? fileName.substring(lastDotIndex) : ''

    // Cắt ngắn tên file và giữ lại phần mở rộng
    const nameWithoutExtension =
        lastDotIndex !== -1 ? fileName.substring(0, lastDotIndex) : fileName
    return nameWithoutExtension.substring(0, 16) + '...' + extension
}

// Hàm lấy icon dựa vào loại file
const getFileIcon = (contentType: string) => {
    switch (contentType) {
        case 'PDF':
            return IMAGES.ic_file_pdf
        case 'EXCEL':
            return IMAGES.ic_file_excel
        case 'WORD':
            return IMAGES.ic_file_word
        case 'POWERPOINT':
            return IMAGES.ic_file_powerpoint
        default:
            return IMAGES.ic_file_generic
    }
}

interface ChatInputProps {
    onSendMessage: (message: string) => Promise<void>
    replyTo?: IFromComment
    onCancelReply?: () => void
    onSelectFile?: () => void
    onSelectMediaGallery?: () => void
    onHeightChange?: (height: number) => void
    onTyping?: (isTyping: boolean) => void
    avatarUrl?: any
}

const ChatInput = memo(
    ({
        onSendMessage,
        replyTo,
        onCancelReply,
        onSelectFile,
        onSelectMediaGallery,
        onHeightChange,
        onTyping,
        avatarUrl
    }: ChatInputProps) => {
        const [message, setMessage] = useState('')
        const [showAttachmentMenu, setShowAttachmentMenu] = useState(false)
        const containerRef = React.useRef<View>(null)
        const typingTimeoutRef = React.useRef<NodeJS.Timeout | null>(null)
        const inactivityTimeoutRef = React.useRef<NodeJS.Timeout | null>(null)

        const handleSendMessage = async () => {
            if (!message.trim()) return

            try {
                await onSendMessage(message.trim())
                setMessage('')

                // Gửi typing: false sau khi gửi tin nhắn
                if (onTyping) {
                    onTyping(false)
                }
            } catch (error) {
                console.error('Failed to send message:', error)
            }
        }

        const handleKeyPress = (
            e: NativeSyntheticEvent<TextInputKeyPressEventData>
        ) => {
            if (e.nativeEvent.key === 'Enter') {
                setMessage((prev) => prev + '\n')
                return
            }
        }

        const toggleAttachmentMenu = () => {
            const newState = !showAttachmentMenu
            setShowAttachmentMenu(newState)

            // Đo chiều cao của container sau khi thay đổi trạng thái
            setTimeout(() => {
                if (containerRef.current && onHeightChange) {
                    containerRef.current.measure((x, y, width, height) => {
                        onHeightChange(height)
                    })
                }
            }, 0)
        }

        const handleSelectFile = () => {
            setShowAttachmentMenu(false)
            if (onSelectFile) {
                onSelectFile()
            }
        }

        const handleSelectMediaGallery = () => {
            setShowAttachmentMenu(false)
            if (onSelectMediaGallery) {
                onSelectMediaGallery()
            }
        }

        // Đo chiều cao ban đầu khi component mount
        React.useEffect(() => {
            if (containerRef.current && onHeightChange) {
                setTimeout(() => {
                    containerRef.current?.measure((_, __, ___, height) => {
                        onHeightChange(height)
                    })
                }, 0)
            }
        }, [onHeightChange])

        // Đo lại chiều cao khi replyTo thay đổi
        React.useEffect(() => {
            if (containerRef.current && onHeightChange) {
                setTimeout(() => {
                    containerRef.current?.measure((_, __, ___, height) => {
                        onHeightChange(height)
                    })
                }, 0)
            }
        }, [replyTo, onHeightChange])

        // Xử lý khi component unmount hoặc WebSocket đóng
        useEffect(() => {
            return () => {
                // Gửi typing: false khi component unmount (WebSocket đóng)
                if (onTyping) {
                    onTyping(false)
                }

                // Xóa các timeout để tránh memory leak
                if (typingTimeoutRef.current) {
                    clearTimeout(typingTimeoutRef.current)
                }
                if (inactivityTimeoutRef.current) {
                    clearTimeout(inactivityTimeoutRef.current)
                }
            }
        }, [onTyping])

        return (
            <View
                ref={containerRef}
                style={{ marginBottom: Mixins.scale(8) }}
                onLayout={() => {
                    if (containerRef.current && onHeightChange) {
                        containerRef.current.measure((_, __, ___, height) => {
                            onHeightChange(height)
                        })
                    }
                }}>
                {replyTo && (
                    <View style={styles.replyContainer}>
                        <View style={styles.replyContent}>
                            <Image
                                //@ts-ignore
                                isLocal
                                source={{
                                    uri: avatarUrl?.backgroundImage
                                }}
                                style={styles.replyAvatar}
                            />
                            <View style={styles.replyTextContainer}>
                                <MyText
                                    category="bold.caption.1"
                                    text={'Admin'}
                                    style={styles.replyToText}
                                />
                                {replyTo.contentType === 'IMAGE' ? (
                                    <View style={styles.replyImageContainer}>
                                        <Image
                                            //@ts-ignore
                                            isLocal={false}
                                            source={{
                                                uri: JSON.parse(replyTo.content)
                                                    .url
                                            }}
                                            style={styles.replyImageThumbnail}
                                            resizeMode="cover"
                                        />
                                        <MyText
                                            category="regular.caption.1"
                                            text={getTruncatedFileName(
                                                JSON.parse(replyTo.content)
                                                    .fileName
                                            )}
                                            style={styles.replyContentText}
                                            numberOfLines={1}
                                        />
                                    </View>
                                ) : replyTo.contentType === 'VIDEO' ? (
                                    <View style={styles.replyImageContainer}>
                                        <Image
                                            //@ts-ignore
                                            isLocal={false}
                                            source={{
                                                uri:
                                                    JSON.parse(replyTo.content)
                                                        .posterImage || ''
                                            }}
                                            style={styles.replyImageThumbnail}
                                            resizeMode="cover"
                                        />
                                        <MyText
                                            category="regular.caption.1"
                                            text="Video"
                                            style={styles.replyContentText}
                                            numberOfLines={1}
                                        />
                                    </View>
                                ) : [
                                      'PDF',
                                      'EXCEL',
                                      'WORD',
                                      'POWERPOINT',
                                      'OTHER_FILE'
                                  ].includes(replyTo.contentType) ? (
                                    <View style={styles.replyImageContainer}>
                                        <Image
                                            //@ts-ignore
                                            isLocal
                                            source={getFileIcon(
                                                replyTo.contentType
                                            )}
                                            style={styles.replyImageThumbnail}
                                            resizeMode="contain"
                                        />
                                        <MyText
                                            category="regular.caption.1"
                                            text={getTruncatedFileName(
                                                JSON.parse(replyTo.content)
                                                    .fileName
                                            )}
                                            style={styles.replyContentText}
                                            numberOfLines={1}
                                        />
                                    </View>
                                ) : (
                                    <MyText
                                        category="regular.caption.1"
                                        text={replyTo.content}
                                        style={styles.replyContentText}
                                        numberOfLines={2}
                                    />
                                )}
                            </View>
                        </View>
                        <TouchableOpacity
                            onPress={onCancelReply}
                            style={styles.cancelReplyButton}>
                            <Image
                                //@ts-ignore
                                isLocal
                                source={IMAGES.ic_close}
                                style={styles.cancelIcon}
                            />
                        </TouchableOpacity>
                    </View>
                )}
                <View style={styles.container}>
                    <TouchableOpacity
                        style={[
                            styles.attachmentButton,
                            {
                                backgroundColor: showAttachmentMenu
                                    ? Colors.BG_SOLID_BRAND
                                    : Colors.BUTTON_SOLID_DISABLE_DIVIDER
                            }
                        ]}
                        onPress={toggleAttachmentMenu}>
                        <Image
                            //@ts-ignore
                            isLocal
                            source={IMAGES.ic_attachment}
                            style={[
                                styles.attachmentIcon,
                                {
                                    tintColor: showAttachmentMenu
                                        ? Colors.WHITE
                                        : Colors.ICON_NEUTRALS_PRIMARY
                                }
                            ]}
                        />
                    </TouchableOpacity>
                    <SearchInput
                        placeholder="Nhập tin nhắn..."
                        isShowSearch={false}
                        isOutline={true}
                        containerStyle={styles.inputContainer}
                        value={message}
                        onChangeText={(text) => {
                            setMessage(text)

                            // Gửi sự kiện typing
                            if (onTyping) {
                                // Xóa timeout cũ nếu có
                                if (typingTimeoutRef.current) {
                                    clearTimeout(typingTimeoutRef.current)
                                }

                                // Nếu có nội dung, gửi typing: true
                                if (text.trim()) {
                                    // Gọi callback typing với isTyping = true
                                    onTyping(true)

                                    // Xóa timeout không hoạt động nếu có
                                    if (inactivityTimeoutRef.current) {
                                        clearTimeout(
                                            inactivityTimeoutRef.current
                                        )
                                    }

                                    // Đặt timeout mới để tránh gửi quá nhiều sự kiện
                                    typingTimeoutRef.current = setTimeout(
                                        () => {
                                            typingTimeoutRef.current = null
                                        },
                                        2000
                                    ) // 2 giây

                                    // Đặt timeout mới để tự động gửi typing: false sau 2 giây không nhập
                                    inactivityTimeoutRef.current = setTimeout(
                                        () => {
                                            // Gửi typing: false sau 2 giây không nhập
                                            onTyping(false)
                                            inactivityTimeoutRef.current = null
                                        },
                                        2000
                                    ) // 2 giây
                                } else {
                                    // Nếu không có nội dung, gửi typing: false
                                    onTyping(false)

                                    // Xóa timeout không hoạt động nếu có
                                    if (inactivityTimeoutRef.current) {
                                        clearTimeout(
                                            inactivityTimeoutRef.current
                                        )
                                        inactivityTimeoutRef.current = null
                                    }
                                }
                            }
                        }}
                        heightInput={Mixins.scale(48)}
                        customSearchBarStyles={styles.searchBarStyles}
                        onClear={() => {
                            setMessage('')
                            // Khi xóa hết nội dung, gửi typing: false
                            if (onTyping) {
                                onTyping(false)
                            }
                        }}
                        multiline={true}
                        blurOnSubmit={false}
                        onSubmitEditing={handleSendMessage}
                        onKeyPress={handleKeyPress}
                    />
                    <TouchableOpacity
                        disabled={!message.trim()}
                        onPress={handleSendMessage}
                        style={[
                            styles.sendButton,
                            message.trim() ? {} : styles.sendButtonDisable
                        ]}>
                        <Image
                            //@ts-ignore
                            isLocal
                            source={IMAGES.ic_send_message}
                            style={[
                                styles.sendIcon,
                                message.trim() ? {} : styles.sendIconDisable
                            ]}
                        />
                    </TouchableOpacity>
                </View>

                {showAttachmentMenu && (
                    <AttachmentOptions
                        onSelectFile={handleSelectFile}
                        onSelectMediaGallery={handleSelectMediaGallery}
                    />
                )}
            </View>
        )
    }
)

const styles = StyleSheet.create({
    replyContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: Mixins.scale(8)
    },
    replyContent: {
        flex: 1,
        flexDirection: 'row',
        alignItems: 'flex-start',
        gap: Mixins.scale(10)
    },
    replyAvatar: {
        width: Mixins.scale(24),
        height: Mixins.scale(24),
        borderRadius: Mixins.scale(12)
    },
    replyTextContainer: {
        flex: 1,
        justifyContent: 'center'
    },
    replyToText: {
        color: Colors.TEXT_PRIMARY
    },
    replyContentText: {
        color: Colors.TEXT_PRIMARY,
        fontSize: Mixins.scaleFont(14),
        lineHeight: Mixins.scaleFont(20)
    },
    replyTimeText: {
        color: Colors.TEXT_DISABLE,
        fontSize: Mixins.scaleFont(12),
        marginTop: Mixins.scale(4)
    },
    cancelReplyButton: {
        padding: Mixins.scale(4)
    },
    cancelIcon: {
        width: Mixins.scale(20),
        height: Mixins.scale(20),
        alignItems: 'center',
        justifyContent: 'center'
    },
    cancelText: {
        fontSize: Mixins.scale(16),
        color: Colors.TEXT_SECONDARY,
        fontWeight: 'bold'
    },
    container: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: Mixins.scale(16)
    },
    attachmentButton: {
        width: Mixins.scale(32),
        height: Mixins.scale(32),
        borderRadius: Mixins.scale(16),
        backgroundColor: Colors.BUTTON_SOLID_DISABLE_DIVIDER,
        alignItems: 'center',
        justifyContent: 'center'
    },
    attachmentIcon: {
        width: Mixins.scale(20),
        height: Mixins.scale(20)
    },
    inputContainer: {
        flex: 1
    },
    searchBarStyles: {
        paddingHorizontal: Mixins.scale(8)
    },
    sendButton: {
        width: Mixins.scale(32),
        height: Mixins.scale(32),
        borderRadius: Mixins.scale(16),
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: Colors.BG_SOLID_BRAND
    },
    sendButtonDisable: {
        backgroundColor: Colors.BG_NEUTRALS_SECONDARY
    },
    sendIcon: {
        width: Mixins.scale(20),
        height: Mixins.scale(20)
    },
    sendIconDisable: {
        tintColor: Colors.ICON_NEUTRALS_PRIMARY
    },
    replyImageContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: Mixins.scale(8),
        maxWidth: '100%',
        flexWrap: 'nowrap'
    },
    replyImageThumbnail: {
        width: Mixins.scale(24),
        height: Mixins.scale(24),
        borderRadius: Mixins.scale(4),
        flexShrink: 0
    }
})

export default ChatInput
